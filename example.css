@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: #f5f5f5;
    min-height: 100vh;
    overflow-x: hidden;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
}

/* Left Section - Gradient Background with Logo and Search */
.left-section {
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    color: white;
    width: 40%;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
}

.left-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%23ffffff" fill-opacity="0.05" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
    background-size: cover;
    background-position: bottom;
    opacity: 0.8;
    pointer-events: none;
}

.logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.rocket-logo {
    margin-bottom: 20px;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

#rocket-img {
    width: 80px;
    height: auto;
    filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
}

.site-name {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    letter-spacing: 1px;
}

.tagline {
    font-size: 18px;
    opacity: 0.9;
    max-width: 300px;
    text-align: center;
    line-height: 1.5;
    font-weight: 300;
}

.search-section {
    width: 100%;
    max-width: 400px;
    position: relative;
    z-index: 1;
}

.search-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
}

.search-container {
    width: 100%;
    position: relative;
}

.search-container::after {
    content: '\f002';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    pointer-events: none;
}

#search-input {
    width: 100%;
    padding: 15px 20px;
    border-radius: 30px;
    border: none;
    font-size: 16px;
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    backdrop-filter: blur(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

#search-input:focus {
    background-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    outline: none;
}

#search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* Right Section - Game Cards */
.right-section {
    width: 60%;
    padding: 40px;
    background-color: #f9f9f9;
    position: relative;
    overflow: hidden;
}

.right-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(138, 43, 226, 0.05) 0%, rgba(138, 43, 226, 0) 70%);
    border-radius: 50%;
    z-index: 0;
}

.main-title {
    font-size: 32px;
    color: #333;
    text-align: center;
    margin-bottom: 40px;
    font-weight: 700;
    position: relative;
}

.main-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    border-radius: 3px;
}

.game-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 1;
}

.game-card {
    background-color: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px 20px;
    text-align: center;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.game-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: rgba(138, 43, 226, 0.2);
}

.game-img-container {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 10px 20px rgba(138, 43, 226, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.game-card:hover .game-img-container {
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(138, 43, 226, 0.3);
}

.game-img-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    z-index: 1;
}

.game-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.game-card:hover .game-img {
    transform: scale(1.1);
}

.game-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    transition: color 0.3s ease;
}

.game-card:hover .game-name {
    color: #4a00e0;
}

.game-currency {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
    background-color: rgba(138, 43, 226, 0.1);
    padding: 5px 12px;
    border-radius: 20px;
    display: inline-block;
    font-weight: 500;
    transition: all 0.3s ease;
}

.game-card:hover .game-currency {
    background-color: rgba(138, 43, 226, 0.15);
    color: #4a00e0;
}

.start-btn {
    display: inline-block;
    background: linear-gradient(90deg, #ff416c, #ff4b2b);
    color: white;
    padding: 10px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 14px;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(255, 65, 108, 0.2);
    position: relative;
    overflow: hidden;
}

.start-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    transition: left 0.7s ease;
}

.start-btn:hover {
    background: linear-gradient(90deg, #ff4b2b, #ff416c);
    box-shadow: 0 8px 20px rgba(255, 65, 108, 0.4);
    transform: translateY(-2px);
}

.start-btn:hover::before {
    left: 100%;
}

/* Mobile Responsive Design */
@media (max-width: 1200px) {
    .game-cards {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 992px) {
    .container {
        flex-direction: column;
    }

    .left-section, .right-section {
        width: 100%;
    }

    .left-section {
        padding: 40px 30px;
        min-height: auto;
    }

    .right-section {
        padding: 40px 30px;
    }

    .main-title {
        font-size: 28px;
        margin-top: 0;
    }

    .logo-section {
        margin-bottom: 40px;
    }

    .search-section {
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .game-cards {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 20px;
    }

    .game-img-container {
        width: 100px;
        height: 100px;
        margin-bottom: 15px;
    }

    .main-title {
        font-size: 24px;
    }

    .main-title::after {
        width: 40px;
        height: 2px;
    }

    .site-name {
        font-size: 30px;
    }

    .tagline {
        font-size: 16px;
    }

    .game-card {
        padding: 20px 15px;
    }

    .game-name {
        font-size: 16px;
    }

    .game-currency {
        font-size: 13px;
        padding: 4px 10px;
        margin-bottom: 15px;
    }

    .start-btn {
        padding: 8px 25px;
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .left-section, .right-section {
        padding: 30px 20px;
    }

    .game-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .game-img-container {
        width: 80px;
        height: 80px;
    }

    .game-name {
        font-size: 15px;
        margin-bottom: 5px;
    }

    .game-currency {
        font-size: 12px;
        padding: 3px 8px;
        margin-bottom: 12px;
    }

    .start-btn {
        padding: 7px 20px;
        font-size: 12px;
    }

    .main-title {
        font-size: 22px;
        margin-bottom: 30px;
    }

    #rocket-img {
        width: 60px;
    }

    .site-name {
        font-size: 26px;
    }

    .tagline {
        font-size: 14px;
    }

    .search-title {
        font-size: 18px;
    }

    #search-input {
        padding: 12px 15px;
        font-size: 14px;
    }
}

@media (max-width: 400px) {
    .game-img-container {
        width: 70px;
        height: 70px;
    }

    .game-card {
        padding: 15px 10px;
    }

    .game-name {
        font-size: 14px;
    }

    .game-currency {
        font-size: 11px;
        padding: 2px 6px;
        margin-bottom: 10px;
    }

    .start-btn {
        padding: 6px 15px;
        font-size: 11px;
    }
}
